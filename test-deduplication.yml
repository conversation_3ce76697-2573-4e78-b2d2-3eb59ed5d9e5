---
# Test playbook to verify service deduplication logic
- name: Test Service Deduplication
  hosts: localhost
  gather_facts: false
  vars:
    # Test case: med-cover-original appears in both bureau group and individual services
    restart_groups: ['bureau']
    restart_services: ['med-cover-original']
  
  tasks:
    - name: Import service restart role for testing
      ansible.builtin.include_role:
        name: projects/medical-backend/roles/04-restart-services
        tasks_from: task-restart-services.yml
      vars:
        # Override to prevent actual restart execution
        final_service_list: []
